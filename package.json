{"name": "binance-futures-trading-strategy", "version": "1.0.0", "description": "基于币安期货API的短线交易策略系统", "main": "dist/main.js", "scripts": {"build": "tsc", "start": "node dist/main.js", "dev": "ts-node src/main.ts", "cli": "ts-node src/cli.ts", "watch": "tsc -w", "test": "jest", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts", "status": "npm run cli status", "monitor": "npm run cli monitor", "close-all": "npm run cli close-all --force"}, "keywords": ["binance", "futures", "trading", "strategy", "cryptocurrency", "typescript"], "author": "Your Name", "license": "MIT", "dependencies": {"axios": "^1.6.2", "binance": "^2.15.22", "binance-api-node": "^0.12.9", "commander": "^11.1.0", "dotenv": "^16.3.1", "node-cron": "^3.0.3", "winston": "^3.11.0"}, "devDependencies": {"@types/jest": "^29.5.8", "@types/node": "^20.10.0", "@types/node-cron": "^3.0.11", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "jest": "^29.7.0", "prettier": "^3.1.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.3.2"}, "engines": {"node": ">=18.0.0"}}