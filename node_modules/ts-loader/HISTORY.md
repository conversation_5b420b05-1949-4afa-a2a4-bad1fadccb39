# History

`ts-loader` was started by [<PERSON>](http://www.jbrantly.com/) back in January 2015. He created the initial codebase, wrote the comparison testpack and merrily maintained it until it reached v0.8.2.

At that point [<PERSON>](https://blog.johnnyreilly.com/) started maintaining ts-loader. [On October 16th 2016 to be exact](https://twitter.com/jbrantly/status/785931975064444928).  The full story of how that came to be can be read here: https://blog.johnnyreilly.com/2016/11/but-you-cant-die-i-love-you-ts-loader.html 

Since that time, <PERSON> (who wrote this and finds it peculiar to refer to himself in the third person) has continued maintaining ts-loader. He appreciates any and all people that contribute. Who knows, maybe one day the mantle will be passed on again and someone else will step up and take ts-loader forwards. Who knows? It could be you ;-) 