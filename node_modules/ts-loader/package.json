{"name": "ts-loader", "version": "8.4.0", "description": "TypeScript loader for webpack", "main": "index.js", "types": "dist", "scripts": {"build": "tsc --version && tsc --project \"./src\"", "lint": "eslint -c .eslintrc.js --ext .ts ./src", "comparison-tests": "git clean -xfd test/comparison-tests && npm link ./test/comparison-tests/testLib && node test/comparison-tests/run-tests.js", "comparison-tests-generate": "git clean -xfd test/comparison-tests && node test/comparison-tests/stub-new-version.js", "execution-tests": "git clean -xfd test/execution-tests && node test/execution-tests/run-tests.js", "test": "git clean -xfd test/comparison-tests && git clean -xfd test/execution-tests && node test/run-tests.js", "clean": "git clean -xfd test/comparison-tests && git clean -xfd test/execution-tests", "docker:build": "docker build -t ts-loader .", "postdocker:build": "docker run -it ts-loader yarn test", "generate-toc": "markdown-toc -i ./README.md && git add README.md && git commit -m \"chore: update docs\""}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{ts,md}": ["prettier --write", "yarn lint", "git add"]}, "repository": {"type": "git", "url": "https://github.com/TypeStrong/ts-loader.git"}, "keywords": ["ts-loader", "typescript-loader", "webpack", "loader", "typescript", "ts"], "engines": {"node": ">=10.0.0"}, "author": "<PERSON> <<EMAIL>> (https://blog.johnnyreilly.com)", "contributors": ["<PERSON> <<EMAIL>> (https://blog.johnnyreilly.com)", "<PERSON> <<EMAIL>> (http://www.jbrantly.com/)"], "license": "MIT", "bugs": {"url": "https://github.com/TypeStrong/ts-loader/issues"}, "homepage": "https://github.com/TypeStrong/ts-loader", "dependencies": {"chalk": "^4.1.0", "enhanced-resolve": "^4.0.0", "loader-utils": "^2.0.0", "micromatch": "^4.0.0", "semver": "^7.3.4"}, "devDependencies": {"@types/micromatch": "^3.1.0", "@types/node": "*", "@types/semver": "^7.3.4", "@types/webpack": "^4.4.30", "@typescript-eslint/eslint-plugin": "^4.0.0", "@typescript-eslint/parser": "^4.0.0", "babel": "^6.0.0", "babel-core": "^6.0.0", "babel-loader": "^7.0.0", "babel-polyfill": "^6.16.0", "babel-preset-es2015": "^6.0.0", "babel-preset-es2016": "^6.16.0", "babel-preset-react": "^6.0.0", "escape-string-regexp": "^2.0.0", "eslint": "^7.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.1.3", "fs-extra": "^7.0.0", "glob": "^7.1.1", "html-webpack-plugin": "^3.2.0", "husky": "^2.0.0", "jasmine-core": "^3.0.0", "karma": "^4.0.0", "karma-chrome-launcher": "^2.2.0", "karma-jasmine": "^2.0.0", "karma-mocha-reporter": "^2.0.0", "karma-sourcemap-loader": "^0.3.6", "karma-webpack": "^4.0.0-rc.5", "lint-staged": "^8.0.0", "markdown-toc": "^1.2.0", "mkdirp": "^0.5.1", "mocha": "^6.0.0", "prettier": "^2.0.5", "rimraf": "^2.6.2", "typescript": "^4.6.3", "webpack": "^4.5.0", "webpack-cli": "^3.1.1"}, "peerDependencies": {"typescript": "*", "webpack": "*"}}