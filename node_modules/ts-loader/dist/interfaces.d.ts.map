{"version": 3, "file": "interfaces.d.ts", "sourceRoot": "", "sources": ["../src/interfaces.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,UAAU,MAAM,YAAY,CAAC;AAE9C,OAAO,EAAE,KAAK,EAAE,MAAM,OAAO,CAAC;AAC9B,OAAO,KAAK,MAAM,MAAM,UAAU,CAAC;AAEnC,MAAM,WAAW,SAAS;IACxB,IAAI,EAAE,MAAM,CAAC;IACb,QAAQ,EAAE,QAAQ,CAAC;IACnB,OAAO,EAAE,MAAM,CAAC;IAChB,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,MAAM,CAAC;IACb,SAAS,EAAE,MAAM,CAAC;IAClB,OAAO,EAAE,MAAM,CAAC;CACjB;AAED,oBAAY,YAAY,GAAG;IACzB,cAAc;IACd,IAAI,EAAE,MAAM,CAAC;IACb,cAAc;IACd,SAAS,EAAE,MAAM,CAAC;CACnB,CAAC;AAEF,oBAAY,qBAAqB,GAAG;IAClC,cAAc;IACd,IAAI,EAAE,MAAM,CAAC;IACb,cAAc;IACd,MAAM,CAAC,EAAE,MAAM,CAAC;CACjB,CAAC;AACF,MAAM,WAAW,YAAY;IAC3B,MAAM,CAAC,EAAE,GAAG,CAAC;IACb,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,OAAO,EAAE,MAAM,CAAC;IAChB,GAAG,CAAC,EAAE;QAAE,KAAK,EAAE,qBAAqB,CAAC;QAAC,GAAG,CAAC,EAAE,qBAAqB,CAAA;KAAE,CAAC;IAEpE,QAAQ,CAAC,EAAE,YAAY,CAAC;IACxB,YAAY,EAAE,MAAM,CAAC;CACtB;AACD,MAAM,WAAW,aAAa;IAC5B,QAAQ,EAAE,MAAM,CAAC;IACjB,MAAM,EAAE,YAAY,EAAE,CAAC;IACvB,UAAU,CAAC,OAAO,EAAE,KAAK,GAAG,IAAI,CAAC;IACjC,QAAQ,CAAC,KAAK,EAAE,YAAY,GAAG,KAAK,GAAG,IAAI,CAAC;IAC5C,WAAW,IAAI,QAAQ,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC;IAC3C,SAAS,IAAI,QAAQ,CAAC,YAAY,GAAG,KAAK,CAAC,GAAG,SAAS,CAAC;IACxD,sBAAsB,IAAI,IAAI,CAAC;IAC/B,SAAS,EAAE;QACT,mBAAmB,EAAE,MAAM,CAAC;QAC5B,8BAA8B,EAAE,MAAM,EAAE,CAAC;KAC1C,CAAC;CACH;AAED,oBAAY,WAAW,GAAG,CACxB,OAAO,EAAE,MAAM,GAAG,SAAS,EAC3B,IAAI,EAAE,MAAM,EACZ,UAAU,EAAE,MAAM,KACf,MAAM,CAAC;AAEZ,MAAM,WAAW,kBAAkB;IACjC,UAAU,CAAC,IAAI,IAAI,CAAC;IACpB,eAAe,CAAC,EAAE,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACvC,oBAAoB,CAAC,EAAE,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAC5C,aAAa,CAAC,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;CACrC;AAED,MAAM,WAAW,aAAc,SAAQ,kBAAkB;IACvD,UAAU,EAAE,UAAU,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;IAC1D,eAAe,EAAE,WAAW,CAC1B,UAAU,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CACnD,CAAC;IACF,QAAQ,CAAC,EAAE,UAAU,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;CACxD;AAED,MAAM,WAAW,kCACf,SAAQ,UAAU,CAAC,oBAAoB,EACrC,kBAAkB;IACpB,QAAQ,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,CAAC;IAClE,KAAK,EAAE,WAAW,CAAC,UAAU,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC,CAAC;IAC7D,eAAe,EAAE,WAAW,CAC1B,UAAU,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CACnD,CAAC;IACF,mBAAmB,EAAE,WAAW,CAC9B,UAAU,CAAC,oBAAoB,CAAC,qBAAqB,CAAC,CACvD,CAAC;IACF,cAAc,EAAE,WAAW,CACzB,UAAU,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAClD,CAAC;IAGF,yBAAyB,EAAE,WAAW,CACpC,UAAU,CAAC,mBAAmB,CAAC,2BAA2B,CAAC,CAC5D,CAAC;IACF,UAAU,EAAE,WAAW,CAAC,UAAU,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC,CAAC;IACtE,qBAAqB,EAAE,WAAW,CAChC,UAAU,CAAC,mBAAmB,CAAC,uBAAuB,CAAC,CACxD,CAAC;IACF,aAAa,EAAE,WAAW,CAAC,UAAU,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,CAAC;CAC7E;AAED,MAAM,WAAW,8BACf,SAAQ,UAAU,CAAC,mBAAmB,EACpC,kBAAkB;CAAG;AAEzB,MAAM,WAAW,SACf,SAAQ,UAAU,CAAC,0CAA0C,CACzD,UAAU,CAAC,wCAAwC,CACpD,EACD,kBAAkB;IACpB,iBAAiB,EAAE,YAAY,CAAC,mBAAmB,CAAC,CAAC;IACrD,mBAAmB,IAAI,IAAI,CAAC;IAC5B,WAAW,EAAE,GAAG,CAAC,WAAW,EAAE,UAAU,CAAC,UAAU,EAAE,CAAC,CAAC;IACvD,WAAW,CAAC,EAAE,UAAU,CAAC,UAAU,CAAC;CACrC;AAED,oBAAY,cAAc,CAAC,CAAC,IAAI,GAAG,CACjC,WAAW,EACX;IAAE,QAAQ,EAAE,MAAM,CAAC;IAAC,SAAS,EAAE,CAAC,EAAE,CAAA;CAAE,CACrC,CAAC;AACF,MAAM,WAAW,YAAY;IAC3B,YAAY,EAAE,cAAc,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC;IAC7D,kBAAkB,EAAE,cAAc,CAAC,UAAU,CAAC,wBAAwB,CAAC,CAAC;IACxE,2BAA2B,EAAE,cAAc,CACzC,UAAU,CAAC,wBAAwB,CACpC,CAAC;IACF,iBAAiB,CACf,QAAQ,EAAE,MAAM,EAChB,SAAS,EAAE,UAAU,CAAC,oBAAoB,GACzC,OAAO,CAAC;IACX,uGAAuG;IACvG,SAAS,EAAE,UAAU,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;IAC7C,4IAA4I;IAC5I,cAAc,EAAE,UAAU,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;CACxD;AAED,MAAM,WAAW,mBAAmB;IAClC,MAAM,EAAE,UAAU,CAAC,UAAU,EAAE,CAAC;IAChC,OAAO,EAAE,GAAG,CAAC,WAAW,EAAE,UAAU,CAAC,UAAU,EAAE,CAAC,CAAC;IACnD,eAAe,EAAE,CAAC,WAAW,GAAG,SAAS,EAAE,UAAU,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC;CACvE;AAED,oBAAY,WAAW,GAAG,MAAM,GAAG;IAAE,kBAAkB,EAAE,GAAG,CAAA;CAAE,CAAC;AAE/D,MAAM,WAAW,4BACf,SAAQ,UAAU,CAAC,4BAA4B,CAC3C,UAAU,CAAC,wCAAwC,CACpD,EACD,YAAY;IACd,WAAW,EAAE,mBAAmB,CAAC;IACjC,YAAY,EAAE,UAAU,CAAC,UAAU,EAAE,CAAC;IACtC,cAAc,EAAE,GAAG,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;IACjD,8BAA8B,EAAE,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;IACvD,iBAAiB,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IAC1C,8BAA8B,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IACvD,qCAAqC,CACnC,cAAc,EAAE,MAAM,GACrB,WAAW,GAAG,SAAS,CAAC;IAC3B,wCAAwC,CACtC,aAAa,EAAE,MAAM,GACpB;QAAE,GAAG,EAAE,WAAW,CAAC;QAAC,UAAU,EAAE,MAAM,GAAG,KAAK,CAAA;KAAE,GAAG,SAAS,CAAC;IAChE,4CAA4C,CAC1C,aAAa,EAAE,MAAM,GACpB;QAAE,GAAG,EAAE,WAAW,CAAC;QAAC,IAAI,EAAE,MAAM,GAAG,SAAS,CAAA;KAAE,GAAG,SAAS,CAAC;IAC9D,0BAA0B,CAAC,cAAc,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,CAAC;IACvE,wCAAwC,CACtC,aAAa,EAAE,MAAM,GACpB,UAAU,CAAC,UAAU,EAAE,CAAC;IAC3B,eAAe,IAAI,IAAI,CAAC;IACxB,4BAA4B,IAAI,IAAI,CAAC;IACrC,UAAU,IAAI,IAAI,CAAC;IACnB,KAAK,IAAI,IAAI,CAAC;CACf;AAED,MAAM,WAAW,cAAc;IAC7B,MAAM,EAAE,UAAU,CAAC,iBAAiB,GAAG,SAAS,CAAC;IACjD,eAAe,CAAC,EAAE,GAAG,CACnB,WAAW,EACX;QAAE,aAAa,EAAE,MAAM,CAAC;QAAC,WAAW,EAAE,MAAM,EAAE,CAAA;KAAE,CACjD,CAAC;IACF,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,QAAQ,CAAC,EAAE,MAAM,EAAE,CAAC;CACrB;AAED,UAAU,kBAAkB,CAAC,CAAC;IAC5B,MAAM,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IACvB,YAAY,EAAE,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;IACnD,8BAA8B,CAC5B,mBAAmB,EAAE,UAAU,CAAC,wBAAwB,GAAG,SAAS,GACnE,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IAClB,KAAK,IAAI,IAAI,CAAC;IACd,aAAa,CAAC,UAAU,EAAE,UAAU,CAAC,eAAe,GAAG,IAAI,CAAC;IAC5D,SAAS,CAAC,SAAS,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC;CAC5C;AACD,UAAU,kBAAkB;IAC1B,GAAG,CACD,SAAS,EAAE,MAAM,GAChB,UAAU,CAAC,uCAAuC,GAAG,SAAS,CAAC;IAClE,GAAG,CACD,SAAS,EAAE,MAAM,EACjB,MAAM,EAAE,UAAU,CAAC,uCAAuC,GACzD,IAAI,CAAC;CACT;AAED,MAAM,WAAW,qBACf,SAAQ,UAAU,CAAC,qBAAqB;IACxC,wBAAwB,EAAE,kBAAkB,CAC1C,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,uCAAuC,CAAC,CAChE,CAAC;IACF,wBAAwB,EAAE,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;IACjE,KAAK,IAAI,IAAI,CAAC;IACd,MAAM,CAAC,eAAe,EAAE,UAAU,CAAC,eAAe,GAAG,IAAI,CAAC;CAC3D;AAED,MAAM,WAAW,UAAU;IACzB,QAAQ,EAAE,OAAO,UAAU,CAAC;IAC5B,eAAe,EAAE,UAAU,CAAC,eAAe,CAAC;IAC5C,qCAAqC;IACrC,6BAA6B,EAAE,CAAC,QAAQ,EAAE,MAAM,KAAK,MAAM,CAAC;IAC5D,aAAa,EAAE,aAAa,CAAC;IAC7B,aAAa,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;IAC3B,qBAAqB,CAAC,EAAE,qBAAqB,CAAC;IAC9C,4BAA4B,CAAC,EAAE,UAAU,CAAC,qCAAqC,CAAC;IAChF;;OAEG;IACH,KAAK,EAAE,OAAO,CAAC;IACf;;OAEG;IACH,aAAa,CAAC,EAAE,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;IACvC;;;;OAIG;IACH,yBAAyB,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;IACxC,YAAY,CAAC,EAAE,8BAA8B,CAAC;IAC9C,eAAe,CAAC,EAAE,UAAU,CAAC,eAAe,GAAG,IAAI,CAAC;IACpD,OAAO,EAAE,MAAM,CAAC;IAChB,eAAe,EAAE,eAAe,CAAC;IACjC,eAAe,CAAC,EAAE,OAAO,CAAC;IAC1B,YAAY,EAAE,UAAU,CAAC,kBAAkB,CAAC;IAC5C,MAAM,EAAE,KAAK,CAAC;IAEd,UAAU,EAAE,OAAO,CAAC;IACpB,SAAS,CAAC,EAAE,SAAS,CAAC;IACtB,8BAA8B,CAAC,EAAE,UAAU,CAAC,8BAA8B,CACxE,UAAU,CAAC,wCAAwC,CACpD,CAAC;IACF,cAAc,CAAC,EAAE,UAAU,CAAC,wCAAwC,CAAC;IACrE,OAAO,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC;IAC7B,2BAA2B,CAAC,EAAE,OAAO,CAAC;IACtC,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAE3B,qBAAqB,CAAC,EAAE,OAAO,CAAC;IAChC,mBAAmB,CAAC,EAAE,4BAA4B,CAAC;IACnD,cAAc,EAAE,MAAM,GAAG,SAAS,CAAC;IAEnC,iBAAiB,EAAE,CAAC,QAAQ,EAAE,MAAM,KAAK,WAAW,CAAC;IAErD,mBAAmB,EAAE,OAAO,CAAC;IAC7B,iBAAiB,EAAE,UAAU,CAAC,iBAAiB,CAAC;IAChD,GAAG,EAAE,MAAM,CAAC,MAAM,CAAC;CACpB;AAED,MAAM,WAAW,kBAAkB;IACjC,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;CACvD;AAED,oBAAY,eAAe,GAAG,GAAG,CAAC,WAAW,EAAE,cAAc,EAAE,CAAC,CAAC;AACjE,oBAAY,sBAAsB,GAAG,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC;AAE9E,oBAAY,QAAQ,GAAG,MAAM,GAAG,MAAM,GAAG,OAAO,CAAC;AAEjD,oBAAY,iBAAiB,GAAG,CAC9B,UAAU,EAAE,MAAM,EAClB,cAAc,EAAE,MAAM,EACtB,eAAe,EAAE,UAAU,CAAC,eAAe,EAC3C,oBAAoB,EAAE,UAAU,CAAC,oBAAoB,KAClD,UAAU,CAAC,uCAAuC,CAAC;AAExD,oBAAY,uBAAuB,GAAG,CACpC,UAAU,EAAE,MAAM,EAClB,cAAc,EAAE,MAAM,EACtB,eAAe,EAAE,UAAU,CAAC,eAAe,EAC3C,oBAAoB,EAAE,UAAU,CAAC,oBAAoB,EACrD,cAAc,EAAE,iBAAiB,KAC9B,UAAU,CAAC,uCAAuC,CAAC;AAExD,oBAAY,mCAAmC,GAAG,CAChD,iBAAiB,EAAE,MAAM,EACzB,cAAc,EAAE,MAAM,EACtB,eAAe,EAAE,UAAU,CAAC,eAAe,EAC3C,oBAAoB,EAAE,UAAU,CAAC,oBAAoB,EACrD,cAAc,EAAE,OAAO,UAAU,CAAC,6BAA6B,KAC5D,UAAU,CAAC,uDAAuD,CAAC;AAExE,MAAM,WAAW,aAAa;IAC5B,MAAM,EAAE,OAAO,CAAC;IAChB,QAAQ,EAAE,QAAQ,CAAC;IACnB,eAAe,EAAE,OAAO,CAAC;IACzB,QAAQ,EAAE,MAAM,CAAC;IACjB,QAAQ,EAAE,MAAM,CAAC;IACjB,UAAU,EAAE,MAAM,CAAC;IACnB,OAAO,EAAE,MAAM,CAAC;IAChB,aAAa,EAAE,OAAO,CAAC;IACvB,iBAAiB,EAAE,MAAM,EAAE,CAAC;IAC5B,WAAW,EAAE,MAAM,EAAE,CAAC;IACtB,cAAc,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,KAAK,KAAK,MAAM,CAAC;IAC9D,uBAAuB,EAAE,OAAO,CAAC;IACjC,MAAM,EAAE,OAAO,CAAC;IAChB,eAAe,EAAE,UAAU,CAAC,eAAe,CAAC;IAC5C,gBAAgB,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC,EAAE,CAAC;IACtC,iBAAiB,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC,EAAE,CAAC;IACvC,aAAa,EAAE,OAAO,CAAC;IACvB,qBAAqB,EACjB,MAAM,GACN,CAAC,CACC,OAAO,EAAE,UAAU,CAAC,OAAO,KACxB,UAAU,CAAC,kBAAkB,GAAG,SAAS,CAAC,CAAC;IACpD,oBAAoB,EAAE,OAAO,CAAC;IAC9B,oBAAoB,EAAE,OAAO,CAAC;IAC9B,uBAAuB,EAAE,OAAO,CAAC;IACjC,iBAAiB,EAAE,OAAO,CAAC;IAC3B,iBAAiB,EAAE,uBAAuB,CAAC;IAC3C,6BAA6B,EAAE,mCAAmC,CAAC;IACnE,yBAAyB,CAAC,EAAE,OAAO,CAAC;CACrC;AAED,MAAM,WAAW,MAAM;IACrB,QAAQ,EAAE,MAAM,CAAC;IACjB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,OAAO,EAAE,MAAM,CAAC;IAChB,YAAY,CAAC,EAAE,IAAI,CAAC;IACpB,gBAAgB,CAAC,EAAE;QACjB;;;WAGG;QACH,OAAO,CAAC,EAAE,UAAU,CAAC,wBAAwB,CAAC;QAC9C,cAAc,CAAC,EAAE,MAAM,CAAC;KACzB,CAAC;CACH;AAED,4BAA4B;AAC5B,oBAAY,OAAO,GAAG,GAAG,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;AAE/C,MAAM,WAAW,cAAc;IAC7B,gBAAgB,EAAE,MAAM,CAAC;IACzB,gBAAgB,EAAE,MAAM,CAAC;IACzB,cAAc,CAAC,EAAE,cAAc,CAAC;IAChC,uBAAuB,CAAC,EAAE,OAAO,CAAC;CACnC;AAED,MAAM,WAAW,QAAQ;IAEvB,6BAA6B,CAC3B,0BAA0B,EAAE,MAAM,EAClC,cAAc,EAAE,MAAM,GAAG,SAAS,EAClC,OAAO,EAAE,UAAU,CAAC,eAAe,EACnC,IAAI,EAAE,UAAU,CAAC,oBAAoB,EACrC,mBAAmB,CAAC,EAAE,UAAU,CAAC,wBAAwB,EACzD,KAAK,CAAC,EAAE,UAAU,CAAC,qCAAqC,EACxD,cAAc,CAAC,EAAE,UAAU,CAAC,UAAU,CAAC,mBAAmB,CAAC,GAC1D,UAAU,CAAC,uDAAuD,CAAC;CACvE;AAED;;;GAGG;AACH,MAAM,WAAW,UAAU;IAEzB,uBAAuB,CAAC,EAAE,CACxB,GAAG,EAAE,UAAU,CAAC,aAAa,GAAG,MAAM,EACtC,kBAAkB,EAAE,UAAU,CAAC,UAAU,CAAC,mBAAmB,CAAC,KAC3D,UAAU,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC;CACjD;AAED,oBAAY,QAAQ,GAAG,OAAO,GAAG,SAAS,CAAC"}