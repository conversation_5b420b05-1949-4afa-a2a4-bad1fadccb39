{"name": "binance-api-node", "version": "0.12.9", "description": "A node API wrapper for Binance", "main": "dist", "files": ["dist", "index.d.ts"], "scripts": {"build": "rm -rf dist && babel src -d dist", "prepare": "yarn build", "test": "ava --timeout=10s -v", "cover": "nyc ava", "report": "yarn cover && nyc report --reporter=text-lcov | coveralls", "lint": "eslint src", "prettier": "prettier --write '{src,test}/**/*.{ts,js}'", "prettier:check": "prettier -l '{src,test}/**/*.{ts,js}'", "ci": "yarn lint && yarn prettier:check && yarn test"}, "dependencies": {"https-proxy-agent": "^5.0.0", "isomorphic-fetch": "^3.0.0", "isomorphic-ws": "^4.0.1", "json-bigint": "^1.0.0", "lodash.zipobject": "^4.1.3", "reconnecting-websocket": "^4.2.0", "ws": "^7.2.0"}, "devDependencies": {"@babel/cli": "^7.7.4", "@babel/core": "^7.7.4", "@babel/polyfill": "^7.7.0", "@babel/preset-env": "^7.7.4", "@babel/register": "^7.7.4", "ava": "^4.1.0", "babel-eslint": "^10.0.3", "babel-plugin-module-resolver": "^3.2.0", "coveralls": "^3.0.9", "dotenv": "^8.2.0", "eslint": "^6.7.1", "eslint-config-prettier": "^6.7.0", "eslint-config-zavatta": "^6.0.3", "nyc": "^14.1.1", "prettier": "^1.19.1"}, "resolutions": {"isomorphic-fetch/node-fetch": "2.6.1"}, "engines": {"yarn": ">= 1.0.0"}, "ava": {"require": ["@babel/register", "@babel/polyfill"], "files": ["test/**/*", "!test/utils.js"]}, "author": "Balthazar Gronon <*****************>", "homepage": "https://github.com/Ashlar/binance-api-node", "license": "MIT"}