{"name": "source-map-loader", "version": "2.0.2", "description": "extracts inlined source map and offers it to webpack", "license": "MIT", "repository": "webpack-contrib/source-map-loader", "author": "<PERSON> @sokra", "homepage": "https://github.com/webpack-contrib/source-map-loader", "bugs": "https://github.com/webpack-contrib/source-map-loader/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 10.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build", "release": "standard-version", "defaults": "webpack-defaults"}, "files": ["dist"], "peerDependencies": {"webpack": "^5.0.0"}, "dependencies": {"abab": "^2.0.5", "iconv-lite": "^0.6.2", "source-map-js": "^0.6.2"}, "devDependencies": {"@babel/cli": "^7.13.16", "@babel/core": "^7.14.2", "@babel/preset-env": "^7.14.2", "@commitlint/cli": "^12.1.4", "@commitlint/config-conventional": "^12.1.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^26.6.3", "cross-env": "^7.0.3", "del": "^6.0.0", "del-cli": "^3.0.1", "eslint": "^7.26.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.23.2", "husky": "^6.0.0", "jest": "^26.6.3", "lint-staged": "^11.0.0", "memfs": "^3.2.2", "npm-run-all": "^4.1.5", "prettier": "^2.3.0", "standard-version": "^9.3.0", "webpack": "^5.37.0"}, "keywords": ["webpack"]}