{"name": "binance", "version": "2.15.22", "description": "Node.js & JavaScript SDK for Binance REST APIs & WebSockets, with TypeScript & end-to-end tests.", "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib/*", "index.js"], "scripts": {"test": "jest", "test:watch": "jest --watch", "clean": "<PERSON><PERSON><PERSON> lib dist", "build": "tsc", "build:clean": "npm run clean && npm run build", "build:watch": "npm run clean && tsc --watch", "pack": "webpack --config webpack/webpack.config.js", "lint": "eslint src", "prepublishOnly": "npm run build:clean", "betapublish": "npm publish --tag beta"}, "author": "<PERSON><PERSON><PERSON> (https://github.com/tiagosiebler)", "contributors": ["<PERSON><PERSON>", "<PERSON> <<EMAIL>> (http://www.cliffordroche.ca)", "<PERSON><PERSON> Hadi H <<EMAIL>> (https://www.commitcode.com)", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> (https://github.com/dylansproule)", "<PERSON><PERSON><PERSON> (http://gavyaggarwal.com/)", "<PERSON> (https://github.com/NeverEnder4)", "<PERSON> <<EMAIL>> (https://github.com/apexearth)", "<PERSON> <<EMAIL>> (https://brunolobo.xyz)", "0xSmartCrypto <<EMAIL>> (https://twitter.com/0xSmartCrypto)"], "dependencies": {"axios": "^1.6.2", "isomorphic-ws": "^4.0.1", "nanoid": "^3.1.30", "ws": "^7.4.0"}, "devDependencies": {"@types/jest": "^29.1.1", "@types/node": "^22.10.2", "@typescript-eslint/eslint-plugin": "^8.18.0", "eslint": "^8.24.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-require-extensions": "^0.1.3", "eslint-plugin-simple-import-sort": "^12.1.1", "jest": "^29.1.1", "ts-jest": "^29.1.1", "typescript": "^4.7.4"}, "optionalDependencies": {"source-map-loader": "^2.0.0", "ts-loader": "^8.0.11", "webpack": "^5.97.1", "webpack-cli": "^5.1.4"}, "keywords": ["binance", "binance api", "binance futures api", "binance spot api", "binance margin api", "api", "websocket", "rest", "rest api", "inverse", "linear", "usdt", "trading bots", "nodejs", "node", "trading", "cryptocurrency", "bitcoin", "best"], "funding": {"type": "individual", "url": "https://github.com/sponsors/tiagosiebler"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/tiagosiebler/binance"}, "bugs": {"url": "https://github.com/tiagosiebler/binance/issues"}, "homepage": "https://github.com/tiagosiebler/binance#readme"}