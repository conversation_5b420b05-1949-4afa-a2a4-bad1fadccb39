{"version": 3, "file": "typeGuards.js", "sourceRoot": "", "sources": ["../../src/util/typeGuards.ts"], "names": [], "mappings": ";;;AAgCA;;;;;;GAMG;AAEH;;GAEG;AAEH,SAAgB,iCAAiC,CAC/C,IAAwB;IAExB,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,KAAK,iBAAiB,CAAC;AACtE,CAAC;AAJD,8EAIC;AAED,SAAgB,iCAAiC,CAC/C,IAAwB;IAExB,OAAO,CACL,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;QACnB,IAAI,CAAC,MAAM,KAAK,CAAC;QACjB,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,KAAK,iBAAiB,CACxC,CAAC;AACJ,CAAC;AARD,8EAQC;AAED,8FAA8F;AAC9F,SAAgB,4BAA4B,CAC1C,IAAwB;IAExB,OAAO,iCAAiC,CAAC,IAAI,CAAC,CAAC;AACjD,CAAC;AAJD,oEAIC;AAED,SAAgB,kBAAkB,CAChC,IAAwB;IAExB,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,KAAK,OAAO,CAAC;AAC5D,CAAC;AAJD,gDAIC;AAED,SAAgB,kBAAkB,CAChC,IAAwB;IAExB,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,KAAK,OAAO,CAAC;AAC5D,CAAC;AAJD,gDAIC;AAED,SAAgB,uBAAuB,CACrC,IAAwB;IAExB,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,KAAK,YAAY,CAAC;AACjE,CAAC;AAJD,0DAIC;AAED,SAAgB,uBAAuB,CACrC,IAAwB;IAExB,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,KAAK,YAAY,CAAC;AACjE,CAAC;AAJD,0DAIC;AAED,SAAgB,4BAA4B,CAC1C,IAAwB;IAExB,OAAO,CACL,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;QACnB,IAAI,CAAC,MAAM,KAAK,CAAC;QACjB,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,KAAK,YAAY,CACnC,CAAC;AACJ,CAAC;AARD,oEAQC;AAED,SAAgB,qCAAqC,CACnD,IAAwB;IAExB,OAAO,CACL,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;QACnB,IAAI,CAAC,MAAM,KAAK,CAAC;QACjB,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CACjE,CAAC;AACJ,CAAC;AARD,sFAQC;AAED;;GAEG;AACH,SAAgB,qBAAqB,CACnC,IAAwB;IAExB,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,KAAK,UAAU,CAAC;AAC/D,CAAC;AAJD,sDAIC;AAED,SAAgB,kCAAkC,CAChD,IAAwB;IAExB,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,KAAK,kBAAkB,CAAC;AACvE,CAAC;AAJD,gFAIC;AAED,SAAgB,0BAA0B,CACxC,IAAwB;IAExB,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;AACjE,CAAC;AAJD,gEAIC;AAED,SAAgB,8BAA8B,CAC5C,IAAwB;IAExB,OAAO,0BAA0B,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC5E,CAAC;AAJD,wEAIC;AAED,SAAgB,iCAAiC,CAC/C,IAAwB;IAExB,OAAO,0BAA0B,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC5E,CAAC;AAJD,8EAIC;AAED,SAAgB,wCAAwC,CACtD,IAAwB;IAExB,OAAO,CACL,8BAA8B,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,KAAK,iBAAiB,CAC7E,CAAC;AACJ,CAAC;AAND,4FAMC;AAED,SAAgB,wCAAwC,CACtD,IAAwB;IAExB,OAAO,CACL,8BAA8B,CAAC,IAAI,CAAC;QACpC,IAAI,CAAC,SAAS,KAAK,yBAAyB,CAC7C,CAAC;AACJ,CAAC;AAPD,4FAOC;AAED,SAAgB,8BAA8B,CAC5C,IAAwB;IAExB,OAAO,CACL,8BAA8B,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,KAAK,eAAe,CAC3E,CAAC;AACJ,CAAC;AAND,wEAMC;AAED,SAAgB,wCAAwC,CACtD,IAAwB;IAExB,OAAO,CACL,8BAA8B,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,KAAK,YAAY,CACxE,CAAC;AACJ,CAAC;AAND,4FAMC;AAED,SAAgB,yCAAyC,CACvD,IAAwB;IAExB,OAAO,CACL,iCAAiC,CAAC,IAAI,CAAC;QACvC,IAAI,CAAC,SAAS,KAAK,gBAAgB,CACpC,CAAC;AACJ,CAAC;AAPD,8FAOC;AAED,SAAgB,sCAAsC,CACpD,IAAwB;IAExB,OAAO,CACL,iCAAiC,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,KAAK,aAAa,CAC5E,CAAC;AACJ,CAAC;AAND,wFAMC;AAED,SAAgB,4CAA4C,CAC1D,IAAwB;IAExB,OAAO,CACL,iCAAiC,CAAC,IAAI,CAAC;QACvC,IAAI,CAAC,SAAS,KAAK,oBAAoB,CACxC,CAAC;AACJ,CAAC;AAPD,oGAOC;AAED,SAAgB,uDAAuD,CACrE,IAAwB;IAExB,OAAO,CACL,iCAAiC,CAAC,IAAI,CAAC;QACvC,IAAI,CAAC,SAAS,KAAK,kCAAkC,CACtD,CAAC;AACJ,CAAC;AAPD,0HAOC;AAED,SAAgB,oDAAoD,CAClE,IAAwB;IAExB,OAAO,CACL,iCAAiC,CAAC,IAAI,CAAC;QACvC,IAAI,CAAC,SAAS,KAAK,uBAAuB,CAC3C,CAAC;AACJ,CAAC;AAPD,oHAOC;AAED,SAAgB,4CAA4C,CAC1D,IAAwB;IAExB,OAAO,CACL,iCAAiC,CAAC,IAAI,CAAC;QACvC,IAAI,CAAC,SAAS,KAAK,kBAAkB,CACtC,CAAC;AACJ,CAAC;AAPD,oGAOC;AAED;;GAEG;AAEH;;GAEG;AACH,SAAgB,sBAAsB,CACpC,IAAkB;IAElB,OAAO,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,gBAAgB,CAAC;AAC/D,CAAC;AAJD,wDAIC;AAED,SAAgB,2BAA2B,CACzC,IAAkB;IAElB,OAAO,CACL,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;QACnB,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACzD,CAAC;AACJ,CAAC;AAPD,kEAOC;AAED;;GAEG;AACH,SAAgB,mBAAmB,CACjC,IAAkB;IAElB,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,KAAK,gBAAgB,CAAC;AAC7D,CAAC;AAJD,kDAIC;AAED;;GAEG;AACH,SAAgB,UAAU,CAAC,IAAkB;IAC3C,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,KAAK,OAAO,CAAC;AACpD,CAAC;AAFD,gCAEC;AAED;;GAEG;AACH,SAAgB,qBAAqB,CACnC,IAAkB;IAElB,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,KAAK,oBAAoB,CAAC;AACjE,CAAC;AAJD,sDAIC;AAED;;GAEG;AACH,SAAgB,wBAAwB,CACtC,IAAkB;IAElB,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,KAAK,uBAAuB,CAAC;AACpE,CAAC;AAJD,4DAIC;AAED;;GAEG;AACH,SAAgB,kBAAkB,CAChC,IAAkB;IAElB,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,KAAK,gBAAgB,CAAC;AAC7D,CAAC;AAJD,gDAIC"}