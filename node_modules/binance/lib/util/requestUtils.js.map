{"version": 3, "file": "requestUtils.js", "sourceRoot": "", "sources": ["../../src/util/requestUtils.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,mCAAgC;AAchC,iDAA6C;AA6C7C,gEAAgE;AAChE,0BAA0B;AAC1B,IAAI;AAEJ,SAAgB,gBAAgB,CAAC,OAA0B;IACzD,QAAQ,OAAO,EAAE;QACf,KAAK,MAAM,CAAC;QACZ,KAAK,OAAO,CAAC;QACb,KAAK,OAAO,CAAC;QACb,KAAK,OAAO,CAAC;QACb,KAAK,OAAO;YACV,OAAO,UAAU,CAAC;QAEpB,KAAK,MAAM,CAAC;QACZ,KAAK,UAAU,CAAC;QAChB,KAAK,OAAO,CAAC;QACb,KAAK,WAAW,CAAC;QACjB,KAAK,MAAM;YACT,OAAO,UAAU,CAAC;QAEpB,KAAK,UAAU,CAAC;QAChB,KAAK,cAAc;YACjB,OAAO,EAAE,CAAC;QAEZ;YACE,2DAA2D;YAC3D,OAAO,UAAU,CAAC;KACrB;AACH,CAAC;AAxBD,4CAwBC;AAED,SAAgB,kBAAkB,CAAC,OAA0B;IAC3D,MAAM,EAAE,GAAG,IAAA,eAAM,EAAC,EAAE,CAAC,CAAC;IACtB,MAAM,UAAU,GAAG,IAAI,GAAG,gBAAgB,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;IAEzD,OAAO,UAAU,CAAC;AACpB,CAAC;AALD,gDAKC;AAED,SAAgB,eAAe,CAC7B,SAAiB,EAAE,EACnB,iBAAiB,GAAG,KAAK,EACzB,eAAwB,KAAK,EAC7B,wBAAiC,KAAK;IAEtC,MAAM,SAAS,GAAG,CAAC,qBAAqB;QACtC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;QACrB,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,WAAW,CAAC,CAAC;IAE5E,OAAO,SAAS;SACb,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;QACX,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;QAC1B,IAAI,iBAAiB,KAAK,IAAI,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;YAC9D,MAAM,IAAI,KAAK,CACb,uDAAuD,CACxD,CAAC;SACH;QACD,MAAM,YAAY,GAAG,YAAY,CAAC,CAAC,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QACtE,OAAO,GAAG,GAAG,IAAI,YAAY,EAAE,CAAC;IAClC,CAAC,CAAC;SACD,IAAI,CAAC,GAAG,CAAC,CAAC;AACf,CAAC;AAtBD,0CAsBC;AAYD,SAAsB,mBAAmB,CACvC,IAAS,EACT,GAAY,EACZ,MAAe,EACf,UAAmB,EACnB,SAAkB,EAClB,qBAA+B,EAC/B,qBAA+B;;;QAE/B,sFAAsF;QACtF,MAAM,iBAAiB,GAAG,MAAA,MAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,UAAU,mCAAI,UAAU,mCAAI,IAAI,CAAC;QAEjE,IAAI,GAAG,IAAI,MAAM,EAAE;YACjB,MAAM,aAAa,mCACd,IAAI,KACP,SAAS,EACT,UAAU,EAAE,iBAAiB,GAC9B,CAAC;YACF,MAAM,gBAAgB,GAAG,eAAe,CACtC,aAAa,EACb,qBAAqB,EACrB,IAAI,EACJ,qBAAqB,CACtB,CAAC;YACF,MAAM,SAAS,GAAG,MAAM,IAAA,0BAAW,EAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;YAC9D,aAAa,CAAC,SAAS,GAAG,SAAS,CAAC;YAEpC,OAAO;gBACL,WAAW,oBAAO,IAAI,CAAE;gBACxB,gBAAgB;gBAChB,SAAS,EAAE,SAAS;gBACpB,SAAS,EAAE,SAAS;gBACpB,UAAU,EAAE,iBAAiB;aAC9B,CAAC;SACH;QAED,OAAO,EAAE,WAAW,EAAE,IAAI,EAAE,gBAAgB,EAAE,SAAS,EAAE,CAAC;;CAC3D;AArCD,kDAqCC;AAED,MAAM,iBAAiB,GAAsC;IAC3D,6BAA6B;IAC7B,IAAI,EAAE,yBAAyB;IAC/B,KAAK,EAAE,yBAAyB;IAChC,KAAK,EAAE,0BAA0B;IACjC,KAAK,EAAE,0BAA0B;IACjC,KAAK,EAAE,0BAA0B;IAEjC,eAAe;IACf,IAAI,EAAE,0BAA0B;IAChC,QAAQ,EAAE,mCAAmC;IAE7C,gBAAgB;IAChB,KAAK,EAAE,0BAA0B;IACjC,SAAS,EAAE,mCAAmC;IAE9C,kBAAkB;IAClB,QAAQ,EAAE,0BAA0B;IACpC,YAAY,EAAE,gCAAgC;IAE9C,mBAAmB;IACnB,IAAI,EAAE,0BAA0B;CACjC,CAAC;AAEF,SAAgB,qBAAqB,CAAC,MAAyB;IAC7D,QAAQ,MAAM,EAAE;QACd,KAAK,MAAM,CAAC;QACZ,KAAK,OAAO,CAAC;QACb,KAAK,OAAO,CAAC;QACb,KAAK,OAAO,CAAC;QACb,KAAK,OAAO,CAAC;QACb;YACE,OAAO,aAAa,CAAC;QAEvB,KAAK,MAAM,CAAC;QACZ,KAAK,UAAU;YACb,OAAO,cAAc,CAAC;QAExB,KAAK,OAAO,CAAC;QACb,KAAK,WAAW;YACd,OAAO,cAAc,CAAC;QAExB,KAAK,UAAU,CAAC;QAChB,KAAK,cAAc;YACjB,OAAO,cAAc,CAAC;KACzB;AACH,CAAC;AAtBD,sDAsBC;AAED,SAAgB,cAAc,CAC5B,UAA6B,EAC7B,iBAAoC;IAEpC,IAAI,iBAAiB,CAAC,OAAO,EAAE;QAC7B,OAAO,iBAAiB,CAAC,OAAO,CAAC;KAClC;IAED,IAAI,iBAAiB,CAAC,UAAU,EAAE;QAChC,OAAO,iBAAiB,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;KACxD;IAED,OAAO,iBAAiB,CAAC,UAAU,CAAC,CAAC;AACvC,CAAC;AAbD,wCAaC;AAED,SAAgB,gBAAgB,CAAC,QAAgB;IAC/C,IAAI,QAAQ,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE;QACpC,OAAO,IAAI,CAAC;KACb;IACD,IAAI,QAAQ,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE;QACxC,OAAO,IAAI,CAAC;KACb;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AARD,4CAQC;AAED,SAAgB,QAAQ,CAAC,QAAa;IACpC,OAAO,CACL,QAAQ,CAAC,OAAO;QAChB,QAAQ,CAAC,OAAO,CAAC,EAAE,KAAK,MAAM;QAC9B,QAAQ,CAAC,OAAO,KAAK,MAAM;QAC3B,QAAQ,CAAC,OAAO,KAAK,IAAI,CAC1B,CAAC;AACJ,CAAC;AAPD,4BAOC;AAED,SAAgB,iBAAiB,CAC/B,eAAgC,EAChC,qBAA6B,EAC7B,MAImB;IAEnB,OAAO,CAAC,IAAI,CACV,aAAa,eAAe,0CAA0C,qBAAqB,gIAAgI,IAAI,CAAC,SAAS,CACvO,MAAM,CACP,EAAE,CACJ,CAAC;AACJ,CAAC;AAdD,8CAcC;AAED,SAAgB,oBAAoB,CAAC,KAAU,EAAE,KAAY;IAC3D,IAAI,KAAK,CAAC,CAAC,EAAE;QACX,OAAO;KACR;IAED,IAAI,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE;QACtC,KAAK,CAAC,CAAC,GAAG,YAAY,CAAC;QACvB,OAAO;KACR;IAED,IAAI,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE;QACzC,KAAK,CAAC,CAAC,GAAG,eAAe,CAAC;QAC1B,OAAO;KACR;IAED,IACE,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;QACxC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAC7B;QACA,KAAK,CAAC,CAAC,GAAG,kBAAkB,CAAC;QAC7B,OAAO;KACR;IAED,sDAAsD;AACxD,CAAC;AAxBD,oDAwBC;AAYD,SAAgB,mBAAmB,CAAC,KAAY;IAC9C,MAAM,CAAC,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,WAAW,CAAC,GAC3D,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACnB,OAAO;QACL,MAAM,EAAE,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM;QACnD,MAAM,EAAE,MAAkB;QAC1B,SAAS,EAAE,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;QACpC,UAAU,EAAE,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC;QACtC,UAAU;QACV,SAAS,EAAE,SAAS,KAAK,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS;QAC5D,WAAW;KACZ,CAAC;AACJ,CAAC;AAZD,kDAYC;AAED,SAAgB,mBAAmB,CACjC,MAAgB,EAChB,UAAkB,EAClB,SAA6B,SAAS,EACtC,YAAgC,SAAS,EACzC,GAAG,WAAiC;IAEpC,OAAO,CAAC,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,WAAW,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC3E,CAAC;AARD,kDAQC;AAED,SAAgB,iBAAiB,CAAC,KAAU,EAAE,KAAY;IACxD,MAAM,EAAE,MAAM,EAAE,GAAG,mBAAmB,CAAC,KAAK,CAAC,CAAC;IAC9C,KAAK,CAAC,QAAQ,GAAG,MAAM,CAAC;IACxB,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;AACtB,CAAC;AAJD,8CAIC;AAED,SAAgB,OAAO,CAAI,EAAW;IACpC,OAAO,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AACvC,CAAC;AAFD,0BAEC"}