{"version": 3, "file": "browser-support.js", "sourceRoot": "", "sources": ["../../src/util/browser-support.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,SAAS,MAAM,CAAC,GAAG;IACjB,MAAM,GAAG,GAAG,IAAI,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACxC,MAAM,OAAO,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;IACpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,MAAM,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;QACpD,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;KAChC;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAsB,WAAW,CAC/B,OAAe,EACf,MAAc;;QAEd,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;QAElC,IAAI,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE;YAClC,MAAM,SAAS,GAAG,6BAA6B,CAAC;YAChD,MAAM,SAAS,GAAG,2BAA2B,CAAC;YAC9C,MAAM,WAAW,GAAG,MAAM,CAAC,SAAS,CAClC,SAAS,CAAC,MAAM,EAChB,MAAM,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,CACjC,CAAC;YACF,MAAM,eAAe,GAAG,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACrD,MAAM,SAAS,GAAG,MAAM,CAAC,eAAe,CAAC,CAAC;YAE1C,MAAM,GAAG,GAAG,MAAM,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAClD,OAAO,EACP,SAAS,EACT,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,EACxD,KAAK,EACL,CAAC,MAAM,CAAC,CACT,CAAC;YAEF,MAAM,SAAS,GAAG,MAAM,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CACnD,mBAAmB,EACnB,GAAG,EACH,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CACxB,CAAC;YAEF,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;SAChE;QAED,MAAM,GAAG,GAAG,MAAM,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAClD,KAAK,EACL,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EACtB,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,EAC3C,KAAK,EACL,CAAC,MAAM,CAAC,CACT,CAAC;QAEF,MAAM,SAAS,GAAG,MAAM,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CACnD,MAAM,EACN,GAAG,EACH,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CACxB,CAAC;QAEF,OAAO,KAAK,CAAC,SAAS,CAAC,GAAG;aACvB,IAAI,CAAC,IAAI,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC,CAAM,EAAE,EAAE,CAC1C,CAAC,IAAI,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAClC;aACA,IAAI,CAAC,EAAE,CAAC,CAAC;IACd,CAAC;CAAA;AApDD,kCAoDC"}