{"version": 3, "file": "BaseRestClient.js", "sourceRoot": "", "sources": ["../../src/util/BaseRestClient.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA,kDAAsE;AAGtE,8DAAsC;AACtC,iDAMwB;AAYxB,MAA8B,cAAc;IAuB1C,YACE,UAA6B,EAC7B,UAA6B,EAAE,EAC/B,iBAAqC,EAAE;QAzBjC,eAAU,GAAW,CAAC,CAAC;QA2B7B,IAAI,CAAC,OAAO,mBACV,UAAU,EAAE,IAAI;YAChB,oDAAoD;YACpD,cAAc,EAAE,OAAO;YACvB,0DAA0D;YAC1D,qBAAqB,EAAE,KAAK;YAC5B,6CAA6C;YAC7C,eAAe,EAAE,IAAI,IAClB,OAAO,CACX,CAAC;QAEF,IAAI,CAAC,oBAAoB;YACvB,gCAAgC;YAChC,OAAO,EAAE,IAAI,GAAG,EAAE,GAAG,CAAC,EACtB,OAAO,EAAE;YACP,uDAAuD;aACxD,IAEE,cAAc,CAClB,CAAC;QAEF,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,OAAO,CAAC;QAC3B,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,UAAU,CAAC;QAEjC,IAAI,IAAI,CAAC,GAAG,EAAE;YACZ,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE;gBACtC,IAAI,CAAC,oBAAoB,CAAC,OAAO,GAAG,EAAE,CAAC;aACxC;YACD,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;SAC9D;QAED,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,UAAU,CAAC;QACxD,IAAI,CAAC,OAAO,GAAG,IAAA,6BAAc,EAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAE7D,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAC5B,MAAM,IAAI,KAAK,CACb,yDAAyD,CAC1D,CAAC;SACH;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,KAAK,IAAI,EAAE;YACzC,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,cAAe,CAAC,CAAC;SACtE;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE;YAClC,IAAI,CAAC,UAAU,GAAG,IAAI,oBAAU,EAAE,CAAC;SACpC;QAED,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAE5B,IAAI,CAAC,gBAAgB,GAAG;YACtB,mBAAmB,EAAE,CAAC;YACtB,sBAAsB,EAAE,CAAC;YACzB,0BAA0B,EAAE,CAAC;YAC7B,sBAAsB,EAAE,CAAC;YACzB,uBAAuB,EAAE,CAAC;YAC1B,sBAAsB,EAAE,CAAC;YACzB,sBAAsB,EAAE,CAAC;YACzB,sBAAsB,EAAE,CAAC;SAC1B,CAAC;IACJ,CAAC;IAMM,aAAa;QAClB,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAEM,kBAAkB;QACvB,uCACK,IAAI,CAAC,gBAAgB,KACxB,WAAW,EAAE,IAAI,CAAC,mBAAmB,IACrC;IACJ,CAAC;IAED;;OAEG;IACI,aAAa;QAClB,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAEM,aAAa,CAAC,KAAa;QAChC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;IAC1B,CAAC;IAEM,GAAG,CAAC,QAAgB,EAAE,MAAY;QACvC,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;IAC7C,CAAC;IAEM,aAAa,CAClB,QAAgB,EAChB,UAA6B,EAC7B,MAAY;QAEZ,MAAM,OAAO,GAAG,IAAA,6BAAc,EAAC,UAAU,EAAE,EAAE,CAAC,CAAC;QAC/C,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IAC7D,CAAC;IAEM,UAAU,CAAC,QAAgB,EAAE,MAAY;QAC9C,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IACnD,CAAC;IAEM,IAAI,CAAC,QAAgB,EAAE,MAAY;QACxC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;IAC9C,CAAC;IAEM,WAAW,CAAC,QAAgB,EAAE,MAAY;QAC/C,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IACpD,CAAC;IAEM,GAAG,CAAC,QAAgB,EAAE,MAAY;QACvC,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;IAC7C,CAAC;IAEM,UAAU,CAAC,QAAgB,EAAE,MAAY;QAC9C,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IACnD,CAAC;IAEM,MAAM,CAAC,QAAgB,EAAE,MAAY;QAC1C,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;IAChD,CAAC;IAEM,aAAa,CAAC,QAAgB,EAAE,MAAY;QACjD,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACU,KAAK,CAChB,MAAc,EACd,QAAgB,EAChB,MAAY,EACZ,SAAmB,EACnB,eAAwB;;YAExB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC,CAAC;YAE3D,IAAI,SAAS,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;gBAC5C,MAAM,IAAI,KAAK,CACb,0DAA0D,CAC3D,CAAC;aACH;YAED,wJAAwJ;YACxJ,MAAM,EAAE,gBAAgB,EAAE,SAAS,EAAE,WAAW,EAAE,GAChD,MAAM,IAAA,kCAAmB,EACvB,MAAM,EACN,IAAI,CAAC,GAAG,EACR,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,OAAO,CAAC,UAAU,EACvB,SAAS,EACT,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAClC,IAAI,CAAC,OAAO,CAAC,qBAAqB,CACnC,CAAC;YAEJ,MAAM,OAAO,GAAG,eAAe,IAAI,IAAI,CAAC,OAAO,CAAC;YAEhD,MAAM,OAAO,mCACR,IAAI,CAAC,oBAAoB,KAC5B,GAAG,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAClC,MAAM,EAAE,MAAM,EACd,IAAI,EAAE,IAAI,GACX,CAAC;YAEF,IAAI,SAAS,EAAE;gBACb,OAAO,CAAC,GAAG;oBACT,GAAG,GAAG,CAAC,gBAAgB,EAAE,YAAY,GAAG,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aAChE;iBAAM,IAAI,MAAM,KAAK,KAAK,IAAI,MAAM,KAAK,QAAQ,EAAE;gBAClD,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;aACzB;iBAAM;gBACL,OAAO,CAAC,IAAI,GAAG,IAAA,8BAAe,EAC5B,WAAW,EACX,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAClC,IAAI,CACL,CAAC;aACH;YAED,eAAe;YACf,yBAAyB;YACzB,oBAAoB;YACpB,QAAQ;YACR,0BAA0B;YAC1B,qBAAqB;YACrB,mBAAmB;YACnB,6BAA6B;YAC7B,2BAA2B;YAC3B,SAAS;YACT,YAAY;YACZ,SAAS;YACT,OAAO;YACP,KAAK;YAEL,OAAO,IAAA,eAAK,EAAC,OAAO,CAAC;iBAClB,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE;gBACjB,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;gBAC3C,IAAI,QAAQ,CAAC,MAAM,IAAI,GAAG,EAAE;oBAC1B,OAAO,QAAQ,CAAC,IAAI,CAAC;iBACtB;gBAED,MAAM,QAAQ,CAAC;YACjB,CAAC,CAAC;iBACD,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE;gBACjB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;oBACvD,OAAO,QAAQ,CAAC;iBACjB;gBAED,oDAAoD;gBACpD,IAAI;oBACF,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,IAAI,QAAQ,CAAC;iBACjE;gBAAC,OAAO,CAAC,EAAE;oBACV,OAAO,CAAC,KAAK,CACX,2CAA2C,EAC3C,IAAI,CAAC,SAAS,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CACjD,CAAC;iBACH;gBACD,OAAO,QAAQ,CAAC;YAClB,CAAC,CAAC;iBACD,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;QACvD,CAAC;KAAA;IAED;;OAEG;IACK,cAAc,CACpB,CAA4C,EAC5C,GAAW;;QAEX,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;QAEzC,IAAI,QAAQ,IAAI,QAAQ,CAAC,OAAO,EAAE;YAChC,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;SAC5C;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,KAAK,KAAK,EAAE;YAC1C,MAAM,CAAC,CAAC;SACT;QAED,uEAAuE;QACvE,IAAI,CAAC,QAAQ,EAAE;YACb,IAAI,CAAC,OAAO,EAAE;gBACZ,MAAM,OAAO,CAAC;aACf;YAED,wCAAwC;YACxC,MAAM,CAAC,CAAC;SACT;QAED,mEAAmE;QACnE,qCAAqC;QACrC,MAAM;YACJ,IAAI,EAAE,MAAA,QAAQ,CAAC,IAAI,0CAAE,IAAI;YACzB,OAAO,EAAE,MAAA,QAAQ,CAAC,IAAI,0CAAE,GAAG;YAC3B,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,OAAO,EAAE,QAAQ,CAAC,OAAO;YACzB,UAAU,EAAE,GAAG;YACf,WAAW,EAAE,OAAO,CAAC,IAAI;YACzB,cAAc,kCACT,IAAI,CAAC,OAAO,KACf,OAAO,EAAE,SAAS,EAClB,UAAU,EAAE,SAAS,GACtB;SACF,CAAC;IACJ,CAAC;IAEO,mBAAmB,CAAC,eAAoC;QAC9D,MAAM,KAAK,GAAwB,EAAE,CAAC;QACtC,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,gBAAgB,EAAE;YAC7C,MAAM,WAAW,GAAG,eAAe,CAAC,SAAS,CAAC,CAAC;YAC/C,MAAM,KAAK,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC;YACpC,IAAI,WAAW,KAAK,SAAS,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;gBAC9C,oJAAoJ;gBACpJ,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;gBACzC,KAAK,CAAC,SAAS,CAAC,GAAG;oBACjB,OAAO,EAAE,IAAI;oBACb,WAAW,EAAE,KAAK;oBAClB,QAAQ,EAAE,WAAW;iBACtB,CAAC;aACH;iBAAM;gBACL,KAAK,CAAC,SAAS,CAAC,GAAG;oBACjB,OAAO,EAAE,KAAK;oBACd,WAAW,EAAE,KAAK;oBAClB,QAAQ,EAAE,WAAW;iBACtB,CAAC;aACH;SACF;QACD,kDAAkD;QAClD,kCAAkC;QAClC,wBAAwB;QACxB,IAAI,CAAC,mBAAmB,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC;IAClD,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,KAAK,IAAI,EAAE;YACzC,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;SAC1B;QAED,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI,EAAE;YACjC,OAAO,IAAI,CAAC,eAAe,CAAC;SAC7B;QAED,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;YAC5D,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC;YACzB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED;;OAEG;IACG,eAAe;;YACnB,IAAI;gBACF,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBACzB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;gBAC9C,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAEvB,MAAM,QAAQ,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;gBACnC,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,GAAG,GAAG,QAAQ,CAAC,CAAC;aAC/C;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,CAAC,CAAC,CAAC;gBACtD,OAAO,CAAC,CAAC;aACV;QACH,CAAC;KAAA;CACF;AAxWD,iCAwWC"}