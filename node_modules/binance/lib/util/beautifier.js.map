{"version": 3, "file": "beautifier.js", "sourceRoot": "", "sources": ["../../src/util/beautifier.ts"], "names": [], "mappings": ";;AACA,uDAAyD;AAEzD,MAAqB,UAAU;IAO7B;QACE,IAAI,CAAC,SAAS,GAAG;YACf,qBAAqB;YACrB,2BAA2B;YAC3B,MAAM;YACN,cAAc;YACd,UAAU;YACV,QAAQ;YACR,kBAAkB;YAClB,cAAc;YACd,cAAc;YACd,UAAU;YACV,eAAe;YACf,iBAAiB;YACjB,cAAc;YACd,iBAAiB;YACjB,MAAM;YACN,cAAc;YACd,SAAS;YACT,iBAAiB;YACjB,UAAU;YACV,QAAQ;YACR,iBAAiB;YACjB,OAAO;YACP,eAAe;YACf,WAAW;YACX,YAAY;YACZ,kBAAkB;YAClB,oBAAoB;YACpB,cAAc;YACd,oCAAoC;YACpC,YAAY;YACZ,aAAa;YAC<PERSON>,MAAM;YACN,QAAQ;YACR,aAAa;YACb,MAAM;YACN,WAAW;YACX,iBAAiB;YACjB,YAAY;YACZ,SAAS;YACT,QAAQ;YACR,YAAY;YACZ,SAAS;YACT,QAAQ;YACR,gBAAgB;YAChB,gBAAgB;YAChB,sBAAsB;YACtB,iBAAiB;YACjB,oBAAoB;YACpB,WAAW;YACX,aAAa;YACb,gBAAgB;YAChB,mBAAmB;YACnB,6BAA6B;YAC7B,kBAAkB;YAClB,QAAQ;YACR,KAAK;YACL,UAAU;YACV,2BAA2B;YAC3B,iBAAiB;YACjB,WAAW;YACX,kBAAkB;YAClB,UAAU;YACV,QAAQ;YACR,aAAa;YACb,UAAU;YACV,QAAQ;YACR,gBAAgB;YAChB,cAAc;YACd,mBAAmB;YACnB,2BAA2B;YAC3B,UAAU;YACV,2BAA2B;YAC3B,gBAAgB;YAChB,MAAM;YACN,WAAW;YACX,eAAe;YACf,gCAAgC;YAChC,eAAe;YACf,kBAAkB;YAClB,SAAS;YACT,gBAAgB;YAChB,aAAa;YACb,eAAe;YACf,gBAAgB;YAChB,OAAO;YACP,aAAa;YACb,oBAAoB;YACpB,UAAU;YACV,KAAK;YACL,kBAAkB;YAClB,aAAa;YACb,mBAAmB;YACnB,gBAAgB;YAChB,kBAAkB;YAClB,qBAAqB;YACrB,qBAAqB;YACrB,oBAAoB;YACpB,UAAU;YACV,WAAW;YACX,SAAS;YACT,sBAAsB;YACtB,iBAAiB;YACjB,uBAAuB;YACvB,UAAU;YACV,gBAAgB;YAChB,aAAa;YACb,eAAe;YACf,4BAA4B;YAC5B,6BAA6B;YAC7B,6BAA6B;YAC7B,0BAA0B;YAC1B,eAAe;YACf,kBAAkB;YAClB,QAAQ;YACR,cAAc;YACd,eAAe;YACf,sBAAsB;YACtB,kBAAkB;YAClB,aAAa;YACb,aAAa;YACb,aAAa;YACb,yBAAyB;YACzB,aAAa;SACd,CAAC;QAEF,6DAA6D;QAC7D,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACjC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,iBAAiB,GAAG,sCAAoB,CAAC;IAChD,CAAC;IAED,oBAAoB,CAAC,GAAoB,EAAE,GAAY;QACrD,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,GAAG,KAAK,EAAE,EAAE;YACvE,MAAM,MAAM,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;YAC/B,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE;gBACjB,OAAO,GAAG,CAAC;aACZ;YACD,OAAO,MAAM,CAAC;SACf;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,IAAiB;QACpC,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACvB,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;SACvC;QACD,MAAM,eAAe,GAAG,EAAE,CAAC;QAC3B,KAAK,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YAC7C,MAAM,IAAI,GAAG,OAAO,GAAG,CAAC;YACxB,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBACtB,eAAe,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;aAC3D;iBAAM,IAAI,GAAG,KAAK,GAAG,IAAI,IAAI,KAAK,QAAQ,EAAE;gBAC3C,eAAe,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;aACpE;iBAAM,IAAI,IAAI,KAAK,QAAQ,EAAE;gBAC5B,eAAe,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;aACvD;iBAAM;gBACL,eAAe,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;aAC5D;SACF;QACD,OAAO,eAAe,CAAC;IACzB,CAAC;IAED,kDAAkD;IAClD,mBAAmB,CAAC,IAAW,EAAE,SAA2B;QAC1D,MAAM,cAAc,GAAU,EAAE,CAAC;QACjC,KAAK,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;YACvC,MAAM,IAAI,GAAG,OAAO,GAAG,CAAC;YACxB,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBACtB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,SAAS,IAAI,GAAG,CAAC,CAAC,CAAC;aACtE;iBAAM,IAAI,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,SAAS,EAAE;gBACvE,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,SAAS,IAAI,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;aACvE;iBAAM;gBACL,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,CAAC;aACrD;SACF;QACD,OAAO,cAAc,CAAC;IACxB,CAAC;IAED,QAAQ,CAAC,IAAS,EAAE,GAAqB;QACvC,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YACtD,OAAO,CAAC,IAAI,CACV,oBAAoB,GAAG,6CAA6C,EACpE,IAAI,EACJ,GAAG,CACJ,CAAC;YACF,OAAO,IAAI,CAAC;SACb;QACD,MAAM,mBAAmB,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;QACxD,IAAI,CAAC,mBAAmB,EAAE;YACxB,sDAAsD;YACtD,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBACvB,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;aACvC;YACD,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,IAAI,EAAE;gBAC7C,OAAO,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;aACxC;YACD,OAAO,IAAI,CAAC,oBAAoB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;SAC7C;QAED,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;YACtB,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;YACxB,MAAM,SAAS,GAAG,OAAO,KAAK,CAAC;YAE/B,IAAI,MAAM,GAAG,mBAAmB,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC;YAC7C,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBACzB,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;aACpB;YAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACzB,IAAI,SAAS,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,EAAE;oBAC5C,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC;iBAClE;qBAAM;oBACL,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;iBAC5D;gBACD,SAAS;aACV;YAED,MAAM,QAAQ,GAAU,EAAE,CAAC;YAC3B,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,EAAE;gBACjD,KAAK,MAAM,YAAY,IAAI,KAAK,EAAE;oBAChC,MAAM,oBAAoB,GACxB,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC;oBACnD,MAAM,YAAY,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;oBAE7C,IAAI,OAAO,YAAY,KAAK,QAAQ,IAAI,YAAY,KAAK,IAAI,EAAE;wBAC7D,MAAM,YAAY,GAAG,EAAE,CAAC;wBACxB,KAAK,MAAM,eAAe,IAAI,YAAY,EAAE;4BAC1C,MAAM,MAAM,GAAG,YAAY,CAAC,eAAe,CAAC,IAAI,eAAe,CAAC;4BAChE,YAAY,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,QAAQ,CAClC,YAAY,CAAC,eAAe,CAAC,EAC7B,MAAM,CACP,CAAC;yBACH;wBACD,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;wBAC5B,SAAS;qBACV;oBAED,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC,CAAC;iBAC1D;aACF;YACD,OAAO,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC;SAC5B;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,iBAAiB,CACf,IAAS,EACT,SAAkB,EAClB,UAAoB;QAEpB,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACvB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;gBACxB,IAAI,KAAK,CAAC,CAAC,EAAE;oBACX,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC;iBAChD;gBACD,OAAO,KAAK,CAAC;YACf,CAAC,CAAC,CAAC;SACJ;aAAM,IAAI,IAAI,CAAC,CAAC,EAAE;YACjB,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,GAAG,OAAO,CAAuB,CAAC;SACpE;aAAM,IAAI,UAAU,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,IAAI,EAAE;YAClE,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAuB,CAAC;SAC7D;QACD,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AA3RD,6BA2RC"}