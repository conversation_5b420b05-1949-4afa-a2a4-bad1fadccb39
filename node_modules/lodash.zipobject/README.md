# lodash.zipobject v4.1.3

The [lodash](https://lodash.com/) method `_.zipObject` exported as a [Node.js](https://nodejs.org/) module.

## Installation

Using npm:
```bash
$ {sudo -H} npm i -g npm
$ npm i --save lodash.zipobject
```

In Node.js:
```js
var zipObject = require('lodash.zipobject');
```

See the [documentation](https://lodash.com/docs#zipObject) or [package source](https://github.com/lodash/lodash/blob/4.1.3-npm-packages/lodash.zipobject) for more details.
